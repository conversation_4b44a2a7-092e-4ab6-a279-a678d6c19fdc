@extends('layouts.app')

@section('content')
<div class="container mx-auto">
    <div class="mb-6">
        <h1 class="text-3xl font-bold">Thêm công việc mới</h1>
        <a href="{{ route('dashboard') }}" class="text-blue-500 hover:underline">
            &larr; Quay lại danh sách
        </a>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        {{ session('error') }}
    </div>
    @endif

    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <form action="{{ route('tasks.store') }}" method="POST">
            @csrf
            <div class="mb-4">
                <label class="block text-gray-700 mb-2" for="title">Tiêu đ<PERSON></label>
                <input
                    type="text"
                    name="title"
                    id="title"
                    value="{{ old('title') }}"
                    class="w-full border border-gray-300 rounded px-3 py-2 @error('title') border-red-500 @enderror"
                    required />
                @error('title')
                <p class="text-red-500 text-xs italic">{{ $message }}</p>
                @enderror
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 mb-2" for="description">Mô tả</label>
                <textarea
                    name="description"
                    id="description"
                    class="w-full border border-gray-300 rounded px-3 py-2 @error('description') border-red-500 @enderror"
                    rows="3">{{ old('description') }}</textarea>
                @error('description')
                <p class="text-red-500 text-xs italic">{{ $message }}</p>
                @enderror
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-gray-700 mb-2" for="start_date">Ngày bắt đầu</label>
                    <input
                        type="date"
                        name="start_date"
                        id="start_date"
                        value="{{ old('start_date', date('Y-m-d')) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2 @error('start_date') border-red-500 @enderror"
                        required />
                    @error('start_date')
                    <p class="text-red-500 text-xs italic">{{ $message }}</p>
                    @enderror
                </div>
                <div>
                    <label class="block text-gray-700 mb-2" for="due_date">Ngày hết hạn</label>
                    <input
                        type="date"
                        name="due_date"
                        id="due_date"
                        value="{{ old('due_date', date('Y-m-d', strtotime('+7 days'))) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2 @error('due_date') border-red-500 @enderror"
                        required />
                    @error('due_date')
                    <p class="text-red-500 text-xs italic">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700 mb-2" for="priority">Mức độ ưu tiên</label>
                <select
                    name="priority"
                    id="priority"
                    class="w-full border border-gray-300 rounded px-3 py-2 @error('priority') border-red-500 @enderror">
                    <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Thấp</option>
                    <option value="medium" {{ old('priority') == 'medium' || !old('priority') ? 'selected' : '' }}>Trung bình</option>
                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>Cao</option>
                </select>
                @error('priority')
                <p class="text-red-500 text-xs italic">{{ $message }}</p>
                @enderror
            </div>
            <div class="flex space-x-4">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    Lưu
                </button>
                <a href="{{ route('dashboard') }}" class="bg-gray-500 hover:bg-gray-700 text-white px-4 py-2 rounded">
                    Hủy
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const startDateInput = document.getElementById('start_date');
        const dueDateInput = document.getElementById('due_date');

        function validateDates() {
            const startDate = new Date(startDateInput.value);
            const dueDate = new Date(dueDateInput.value);

            if (startDate && dueDate && dueDate < startDate) {
                // Tự động điều chỉnh ngày hết hạn bằng ngày bắt đầu
                dueDateInput.value = startDateInput.value;

                // Hiển thị thông báo
                showNotification('Ngày hết hạn đã được tự động điều chỉnh thành ngày bắt đầu', 'warning');
            }
        }

        function showNotification(message, type = 'info') {
            // Tạo notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'warning' ? 'bg-yellow-500 text-white' : 'bg-blue-500 text-white'
        }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Tự động ẩn sau 3 giây
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Lắng nghe sự kiện thay đổi ngày
        startDateInput.addEventListener('change', validateDates);
        dueDateInput.addEventListener('change', validateDates);

        // Kiểm tra ngay khi trang load
        validateDates();
    });
</script>
@endsection