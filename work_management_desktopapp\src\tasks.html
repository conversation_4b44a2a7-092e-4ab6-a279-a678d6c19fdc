<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Danh sách công việc - Ứng dụng Quản lý Công việc</title>
  <link rel="stylesheet" href="css/tasks.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>Quản lý Công việc</h1>
      <div class="user-info">
        <button class="logout-btn" id="logoutBtn">Đăng xuất</button>
      </div>
    </header>

    <button class="add-task-btn" id="openModalBtn">Thêm công việc mới</button>

    <div class="card">
      <div id="loading" class="loading">
        <div class="spinner"></div>
        <p><PERSON>ang tải dữ liệu...</p>
      </div>

      <div id="errorMessage" class="error-message"></div>

      <div id="tasksContainer">
        <table id="tasksTable">
          <thead>
            <tr>
              <th>ID</th>
              <th>Tiêu đề</th>
              <th>Ngày bắt đầu</th>
              <th>Ngày kết thúc</th>
              <th>Trạng thái</th>
              <th>Mức độ ưu tiên</th>
              <th>Thao tác</th>
            </tr>
          </thead>
          <tbody id="tasksList">
            <!-- ... -->
          </tbody>
        </table>
        <div id="noTasks" class="no-tasks" style="display: none;">
          <p>Không có công việc nào. Hãy thêm công việc mới!</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal thêm công việc -->
  <div id="addTaskModal" class="modal">
    <div class="modal-content">
      <span class="close" id="closeModal">&times;</span>
      <h2>Thêm công việc mới</h2>
      <form id="addTaskForm">
        <div class="form-group">
          <label for="title">Tiêu đề</label>
          <input type="text" id="title" name="title" required>
        </div>
        <div class="form-group">
          <label for="description">Mô tả</label>
          <textarea id="description" name="description"></textarea>
        </div>
        <div class="form-group">
          <label for="startDate">Ngày bắt đầu</label>
          <input type="date" id="startDate" name="start_date" required>
        </div>
        <div class="form-group">
          <label for="dueDate">Ngày kết thúc</label>
          <input type="date" id="dueDate" name="due_date" required>
        </div>
        <div class="form-group">
          <label for="priority">Mức độ ưu tiên</label>
          <select id="priority" name="priority" required>
            <option value="low">Thấp</option>
            <option value="medium" selected>Trung bình</option>
            <option value="high">Cao</option>
          </select>
        </div>
        <div class="form-group">
          <label for="status">Trạng thái</label>
          <select id="status" name="status" required>
            <option value="pending" selected>Chờ xử lý</option>
            <option value="in_progress">Đang thực hiện</option>
            <option value="completed">Hoàn thành</option>
          </select>
        </div>
        <div class="form-group" id="userSelectGroup" style="display: none;">
          <label for="userId">Giao cho người dùng</label>
          <select id="userId" name="user_id">
            <option value="">Chọn người dùng...</option>
          </select>
        </div>
        <button type="submit" class="submit-btn">Thêm công việc</button>
      </form>
      <div id="modalError" class="error-message"></div>
    </div>
  </div>

  <!-- Modal sửa công việc -->
  <div id="editTaskModal" class="modal">
    <div class="modal-content">
      <span class="close" id="closeEditModal">&times;</span>
      <h2>Sửa công việc</h2>
      <form id="editTaskForm">
        <input type="hidden" id="editTaskId" name="taskId">
        <div class="form-group">
          <label for="editTitle">Tiêu đề</label>
          <input type="text" id="editTitle" name="title" required>
        </div>
        <div class="form-group">
          <label for="editDescription">Mô tả</label>
          <textarea id="editDescription" name="description"></textarea>
        </div>
        <div class="form-group">
          <label for="editStartDate">Ngày bắt đầu</label>
          <input type="date" id="editStartDate" name="start_date" required>
        </div>
        <div class="form-group">
          <label for="editDueDate">Ngày kết thúc</label>
          <input type="date" id="editDueDate" name="due_date" required>
        </div>
        <div class="form-group">
          <label for="editPriority">Mức độ ưu tiên</label>
          <select id="editPriority" name="priority" required>
            <option value="low">Thấp</option>
            <option value="medium">Trung bình</option>
            <option value="high">Cao</option>
          </select>
        </div>
        <div class="form-group">
          <label for="editStatus">Trạng thái</label>
          <select id="editStatus" name="status" required>
            <option value="pending">Chờ xử lý</option>
            <option value="in_progress">Đang thực hiện</option>
            <option value="completed">Hoàn thành</option>
          </select>
        </div>
        <div class="form-group" id="editUserSelectGroup" style="display: none;">
          <label for="editUserId">Giao cho người dùng</label>
          <select id="editUserId" name="user_id">
            <option value="">Chọn người dùng...</option>
          </select>
        </div>
        <button type="submit" class="submit-btn">Cập nhật công việc</button>
      </form>
      <div id="editModalError" class="error-message"></div>
    </div>
  </div>

  <!-- Dialog xác nhận xóa -->
  <div id="confirmDialog" class="confirm-dialog">
    <div class="confirm-content">
      <h3>Xác nhận xóa</h3>
      <p>Bạn có chắc chắn muốn xóa công việc này không?</p>
      <div class="confirm-buttons">
        <button id="confirmYes" class="confirm-yes">Xóa</button>
        <button id="confirmNo" class="confirm-no">Hủy</button>
      </div>
    </div>
  </div>

  <script src="js/tasks.js"></script>
</body>
</html>
