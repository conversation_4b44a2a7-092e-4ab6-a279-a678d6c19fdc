/* Dashboard CSS */
:root {
    --primary-color: #3490dc;
    --primary-hover: #2779bd;
    --secondary-color: #f8fafc;
    --text-color: #333;
    --border-color: #e2e8f0;
    --success-color: #38c172;
    --danger-color: #e3342f;
    --warning-color: #f6993f;
    --info-color: #6cb2eb;
}

/* General Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f8fafc;
    margin: 0;
    padding: 0;
}

.app {
    display: flex;
    min-height: calc(100vh - 64px);
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: white;
    border-right: 1px solid var(--border-color);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.sidebar__logo {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.sidebar__title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-left: 0.75rem;
}

/* Main Content Area */
.main {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

/* Navigation Bar */
.nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.nav__date-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.nav__controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.nav__arrows {
    display: flex;
    gap: 0.25rem;
}

.nav__date {
    font-size: 1.25rem;
    font-weight: 600;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    position: relative;
    z-index: 1003;
}

.button:active {
    transform: translateY(1px);
}

.button--primary {
    background-color: var(--primary-color);
    color: white;
}

.button--primary:hover {
    background-color: var(--primary-hover);
}

.button--secondary {
    background-color: white;
    border-color: var(--border-color);
}

.button--secondary:hover {
    background-color: var(--secondary-color);
}

.button--icon {
    padding: 0.375rem;
    position: relative;
    z-index: 1004;
}

.button--lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

.button--sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.button__icon {
    width: 1.25rem;
    height: 1.25rem;
    pointer-events: none;
}

/* Calendar Styles */
.calendar {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    min-height: 500px;
    padding: 1rem;
}

.mini-calendar {
    margin-top: 1.5rem;
    background-color: white;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.mini-calendar__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.mini-calendar__date {
    font-weight: 600;
}

.mini-calendar__controls {
    display: flex;
    gap: 0.25rem;
}

.mini-calendar__day-of-week-list {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    list-style: none;
    padding: 0;
    margin: 0 0 0.5rem 0;
    text-align: center;
}

.mini-calendar__day-of-week {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 600;
    padding: 0.25rem 0;
}

.mini-calendar__day-list {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
    list-style: none;
    padding: 0;
    margin: 0;
}

.mini-calendar__day {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2rem;
    width: 1rem;
    border-radius: 50%;
    font-size: 0.875rem;
    cursor: pointer;
    margin: 0 auto;
    transition: all 0.2s ease;
    position: relative;
}

.mini-calendar__day:hover {
    background-color: #f3f4f6;
}

.mini-calendar__day.prev-month,
.mini-calendar__day.next-month {
    color: #cbd5e0;
}

.mini-calendar__day.today {
    background-color: #ebf5ff;
    font-weight: 600;
}

.mini-calendar__day.selected {
    background-color: var(--primary-color);
    color: white;
}

/* Month Grid Styles */
.month-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: var(--border-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    overflow: hidden;
}

.month-grid__header {
    background-color: #f8fafc;
    padding: 0.75rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: #718096;
}

.month-grid__day {
    background-color: white;
    min-height: 100px;
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
}

.month-grid__day.prev-month,
.month-grid__day.next-month {
    background-color: #f8fafc;
}

.month-grid__day.today {
    background-color: #ebf5ff;
}

.month-grid__day-number {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.month-grid__day.prev-month .month-grid__day-number,
.month-grid__day.next-month .month-grid__day-number {
    color: #cbd5e0;
}

.month-grid__events {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    overflow: hidden;
}

/* Select Styles */
.select {
    position: relative;
    display: inline-block;
}

.select__select {
    appearance: none;
    padding: 0.5rem 2rem 0.5rem 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
    background-color: white;
    font-size: 0.875rem;
    cursor: pointer;
}

.select__icon {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    width: 1rem;
    height: 1rem;
}

/* Dialog Styles */
.dialog {
    border: none;
    border-radius: 0.5rem;
    padding: 0;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 2000; /* Tăng z-index để đảm bảo dialog hiển thị trên cùng */
    position: relative;
}

.dialog::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1900; /* Đảm bảo backdrop có z-index cao hơn các nút */
}

/* Overlay để che phủ toàn bộ trang khi popup hiển thị */
.page-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1500; /* Cao hơn các nút nhưng thấp hơn dialog */
    display: none; /* Mặc định ẩn */
}

.dialog__wrapper {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1001;
}

.dialog__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.dialog__title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.dialog__content {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: 60vh;
}

.dialog__footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    position: relative;
    z-index: 1002;
}

.dialog__actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    position: relative;
    z-index: 1005;
}

.dialog__actions .button {
    position: relative;
    z-index: 1006;
}

/* Form Styles */
.form__fields {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.form__field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form__label {
    font-weight: 500;
    font-size: 0.875rem;
}

.form__split {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.input {
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.input--fill {
    width: 100%;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .app {
        flex-direction: column;
    }

    .desktop-only {
        display: none;
    }

    .nav {
        padding: 0.75rem 0;
    }
}

@media (min-width: 769px) {
    .mobile-only {
        display: none;
    }
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.fab:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.fab__icon {
    width: 1.5rem;
    height: 1.5rem;
}

/* Logout Button */
#logout-button {
    background-color: #f56565;
    color: white;
    border: none;
}

#logout-button:hover {
    background-color: #e53e3e;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 0.375rem;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.notification--visible {
    transform: translateX(0);
}

.notification--success {
    background-color: #f0fff4;
    border-left: 4px solid var(--success-color);
    color: #276749;
}

.notification--error {
    background-color: #fff5f5;
    border-left: 4px solid var(--danger-color);
    color: #c53030;
}

.notification--info {
    background-color: #ebf8ff;
    border-left: 4px solid var(--info-color);
    color: #2c5282;
}

.notification--warning {
    background-color: #fffaf0;
    border-left: 4px solid var(--warning-color);
    color: #c05621;
}

/* Month Grid Event Styles */
.month-grid__event {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    color: white;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.month-grid__event:hover {
    filter: brightness(1.1);
}

.month-grid__more-events {
    font-size: 0.75rem;
    color: #718096;
    text-align: center;
    cursor: pointer;
}

.month-grid__more-events:hover {
    color: var(--primary-color);
}

/* Event Details Modal */
.event-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000; /* Tăng z-index để đảm bảo hiển thị trên cùng */
}

.event-details-modal__content {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    width: 100%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.event-details-modal__close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    color: #718096;
}

.event-details-modal__title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    padding-right: 2rem;
}

.event-details-modal__details {
    margin-bottom: 1.5rem;
}

.event-details-modal__details p {
    margin: 0.5rem 0;
}

.event-details-modal__actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* Day Events Modal */
.day-events-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000; /* Tăng z-index để đảm bảo hiển thị trên cùng */
}

.day-events-modal__content {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    width: 100%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    max-height: 80vh;
    overflow-y: auto;
}

.day-events-modal__close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    color: #718096;
}

.day-events-modal__title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    padding-right: 2rem;
}

.day-events-modal__list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.day-events-modal__item {
    padding: 0.75rem;
    border-radius: 0.375rem;
    background-color: #f8fafc;
    border-left: 4px solid var(--primary-color);
    cursor: pointer;
}

.day-events-modal__item:hover {
    /* Loại bỏ hiệu ứng hover */
    background-color: #f8fafc;
}

.day-events-modal__item-title {
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 0.5rem 0;
}

.day-events-modal__item-details {
    display: flex;
    gap: 0.5rem;
}

.day-events-modal__item-status {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 1rem;
    background-color: #e2e8f0;
}

.status-completed {
    background-color: #c6f6d5;
    color: #276749;
}

.status-in_progress {
    background-color: #feebc8;
    color: #c05621;
}

.status-pending {
    background-color: #e2e8f0;
    color: #4a5568;
}

/* Button Danger */
.button--danger {
    background-color: #f56565;
    color: white;
}

.button--danger:hover {
    background-color: #e53e3e;
}

/* Week View Styles */
.week-view {
    display: grid;
    grid-template-columns: 60px repeat(7, 1fr);
    gap: 1px;
    background-color: var(--border-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
    height: 600px;
}

.week-view__time-column {
    display: flex;
    flex-direction: column;
    background-color: #f8fafc;
    position: sticky;
    left: 0;
    z-index: 5;
}

.week-view__day-column {
    display: flex;
    flex-direction: column;
    background-color: white;
    position: relative;
}

.week-view__header {
    padding: 0.75rem 0.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: #718096;
    background-color: #f8fafc;
    border-bottom: 1px solid var(--border-color);
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: sticky;
    top: 0;
    z-index: 5;
}

.week-view__header--today {
    background-color: #ebf5ff;
    color: var(--primary-color);
}

.week-view__header--time {
    border-right: 1px solid var(--border-color);
}

.week-view__day-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 1440px; /* 24 giờ x 60px */
}

.week-view__time-slot {
    height: 60px;
    border-bottom: 1px solid var(--border-color);
    padding: 0.25rem;
    font-size: 0.75rem;
    color: #718096;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.week-view__time-column .week-view__time-slot {
    justify-content: flex-end;
    padding-right: 0.5rem;
    border-right: 1px solid var(--border-color);
}

.week-view__event {
    position: absolute;
    left: 2px;
    right: 2px;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    color: white;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    z-index: 10;
    text-align: center;
}

.week-view__event:hover {
    /* Loại bỏ hiệu ứng hover */
    filter: none;
}

/* Day View Styles */
.day-view {
    display: grid;
    grid-template-columns: 60px 1fr;
    gap: 1px;
    background-color: var(--border-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
    height: 600px;
}

.day-view__time-column {
    display: flex;
    flex-direction: column;
    background-color: #f8fafc;
    position: sticky;
    left: 0;
    z-index: 5;
}

.day-view__day-column {
    display: flex;
    flex-direction: column;
    background-color: white;
    position: relative;
}

.day-view__header {
    padding: 0.75rem 0.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: #718096;
    background-color: #f8fafc;
    border-bottom: 1px solid var(--border-color);
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: sticky;
    top: 0;
    z-index: 5;
}

.day-view__header--today {
    background-color: #ebf5ff;
    color: var(--primary-color);
}

.day-view__header--time {
    border-right: 1px solid var(--border-color);
}

.day-view__day-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 1440px; /* 24 giờ x 60px */
}

.day-view__time-slot {
    height: 60px;
    border-bottom: 1px solid var(--border-color);
    padding: 0.25rem;
    font-size: 0.75rem;
    color: #718096;
    display: flex;
    align-items: flex-start;
}

.day-view__time-column .day-view__time-slot {
    justify-content: flex-end;
    padding-right: 0.5rem;
    border-right: 1px solid var(--border-color);
}

.day-view__event {
    position: absolute;
    left: 4px;
    right: 4px;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    color: white;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    z-index: 10;
    text-align: center;
}

/* Utility Classes */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-end {
    align-items: flex-end;
}

.ml-auto {
    margin-left: auto;
}

.mb-2 {
    margin-bottom: 0.5rem;
}
